{"indexes": [{"collectionGroup": "tripItineraries", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tripId", "order": "ASCENDING"}, {"fieldPath": "day", "order": "ASCENDING"}, {"fieldPath": "time", "order": "ASCENDING"}]}, {"collectionGroup": "tripItineraries", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tripId", "order": "ASCENDING"}, {"fieldPath": "day", "order": "ASCENDING"}, {"fieldPath": "time", "order": "ASCENDING"}]}, {"collectionGroup": "userTrips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "tripId", "order": "ASCENDING"}]}, {"collectionGroup": "members", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "joinedAt", "order": "DESCENDING"}]}, {"collectionGroup": "members", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "joinedAt", "order": "ASCENDING"}]}, {"collectionGroup": "squads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "squadId", "order": "ASCENDING"}, {"fieldPath": "joinedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tripSavings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "tripId", "order": "ASCENDING"}]}, {"collectionGroup": "invitations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inviteeId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "invitations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "squadId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-links", "queryScope": "COLLECTION", "fields": [{"fieldPath": "squadId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "invitationId", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "subscriptionStatus", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "subscriptionPlan", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionStatus", "order": "ASCENDING"}, {"fieldPath": "subscriptionPlan", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "precedence", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "precedence", "order": "ASCENDING"}]}, {"collectionGroup": "userPreferences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "theme", "order": "ASCENDING"}]}, {"collectionGroup": "userPreferences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "reviewDate", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "squadId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "endDate", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "localExperienceBookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "localExperienceBookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "localExperienceBookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "bookedAt", "order": "DESCENDING"}]}, {"collectionGroup": "localExperienceBookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "experienceId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "localExperienceBookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "experienceId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "localExperienceBookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "experienceId", "order": "ASCENDING"}, {"fieldPath": "bookedAt", "order": "DESCENDING"}]}, {"collectionGroup": "localExperienceBookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "bookedAt", "order": "DESCENDING"}]}, {"collectionGroup": "localExperienceBookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "bookedAt", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "pricing.basePrice", "order": "ASCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "pricing.basePrice", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "location.city", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "location.city", "order": "ASCENDING"}, {"fieldPath": "pricing.basePrice", "order": "ASCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "location.city", "order": "ASCENDING"}, {"fieldPath": "pricing.basePrice", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "location.city", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "categories", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "categories", "arrayConfig": "CONTAINS"}, {"fieldPath": "pricing.basePrice", "order": "ASCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "categories", "arrayConfig": "CONTAINS"}, {"fieldPath": "pricing.basePrice", "order": "DESCENDING"}]}, {"collectionGroup": "localExperiences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "categories", "arrayConfig": "CONTAINS"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "bookedAt", "order": "ASCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "bookedAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "paymentStatus", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isAIResponse", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "messageType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "senderId", "order": "ASCENDING"}, {"fieldPath": "isAIResponse", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "userSubscriptions", "fieldPath": "subscriptionStatus", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userSubscriptions", "fieldPath": "subscriptionPlan", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userSubscriptions", "fieldPath": "subscriptionCurrentPeriodEnd", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userPreferences", "fieldPath": "userId", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userPreferences", "fieldPath": "theme", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "localExperienceBookings", "fieldPath": "status", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "localExperienceBookings", "fieldPath": "date", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "localExperienceBookings", "fieldPath": "experienceId", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "localExperienceBookings", "fieldPath": "bookedAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "localExperienceBookings", "fieldPath": "pricing.total", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userPreferences", "fieldPath": "travelPreferences", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userPreferences", "fieldPath": "updatedAt", "indexes": [{"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "reviews", "fieldPath": "userId", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "reviews", "fieldPath": "reviewDate", "indexes": [{"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "reviews", "fieldPath": "rating", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "messages", "fieldPath": "isAIResponse", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "messages", "fieldPath": "messageType", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "messages", "fieldPath": "aiPrompt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userAiUsage", "fieldPath": "trip_chat.dailyUsage", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userAiUsage", "fieldPath": "trip_chat.isProcessing", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userAiUsage", "fieldPath": "trip_chat.lastRequestTime", "indexes": [{"order": "DESCENDING", "queryScope": "COLLECTION"}]}]}